using System;
using System.Collections.Generic;
using System.Globalization;
namespace Course
{
    class Program
    {
        static List<Tarefa> tarefas = new List<Tarefa>();

        static void Main(string[] args)
        {
            bool executando = true;
            while (executando)
            {
                Console.Clear();
                Console.WriteLine("====================== Gerenciador de Tarefas ======================");
                Console.Write("1 - Adicionar nova Tarefa\n2 - Listar Tarefas\n3 - Marcar Tarefa como Concluida\n4 - Desmarcar Tarefa como Concluida\n5 - Remover Tarefa\n6 - Sair\nEscolha uma opção: ");

                string opcao = Console.ReadLine();

                switch (opcao)
                {
                    case "1":
                        AdicionarTarefa();
                        break;
                    case "2":
                        ListarTarefas();
                        break;
                    case "3":
                        MarcarConcluida();
                        break;
                    case "4":
                        DesmarcarConcluida();
                        break;
                    case "5":
                        RemoverTarefa();
                        break;
                    case "6":
                        executando = false;
                        break;
                    default:
                        Console.WriteLine("Opção inválida! Pressione qualquer tecla para continuar...");
                        Console.ReadKey();
                        break;
                }
            }
        }

        static void AdicionarTarefa()
        {
            Console.Clear();
            Console.WriteLine("=============== Adicionar Nova Tarefa ===============");
            Console.Write("Digite o título da tarefa: ");
            string titulo = Console.ReadLine();

            Console.Write("Digite a descrição da tarefa: ");
            string descricao = Console.ReadLine();

            if (!string.IsNullOrWhiteSpace(titulo))
            {
                Tarefa novaTarefa = new Tarefa(titulo, descricao);
                tarefas.Add(novaTarefa);
                Console.WriteLine("Tarefa adicionada com sucesso!");
            }
            else
            {
                Console.WriteLine("Título não pode estar vazio!");
            }

            Console.WriteLine("Pressione qualquer tecla para continuar...");
            Console.ReadKey();
        }

        static void ListarTarefas()
        {
            Console.Clear();
            Console.WriteLine("=============== Lista de Tarefas ===============");

            if (tarefas.Count == 0)
            {
                Console.WriteLine("Nenhuma tarefa encontrada.");
            }
            else
            {
                for (int i = 0; i < tarefas.Count; i++)
                {
                    Console.WriteLine($"\n--- Tarefa {i + 1} ---");
                    Console.WriteLine(tarefas[i].ToString());
                }
            }

            Console.WriteLine("\nPressione qualquer tecla para continuar...");
            Console.ReadKey();
        }

        static void MarcarConcluida()
        {
            Console.Clear();
            Console.WriteLine("=============== Marcar Tarefa como Concluída ===============");

            if (tarefas.Count == 0)
            {
                Console.WriteLine("Nenhuma tarefa encontrada.");
                Console.WriteLine("Pressione qualquer tecla para continuar...");
                Console.ReadKey();
                return;
            }

            ListarTarefasComIndice();
            Console.Write("Digite o número da tarefa para marcar como concluída: ");

            if (int.TryParse(Console.ReadLine(), out int indice) && indice > 0 && indice <= tarefas.Count)
            {
                tarefas[indice - 1].Concluida = true;
                Console.WriteLine("Tarefa marcada como concluída!");
            }
            else
            {
                Console.WriteLine("Número inválido!");
            }

            Console.WriteLine("Pressione qualquer tecla para continuar...");
            Console.ReadKey();
        }

        static void DesmarcarConcluida()
        {
            Console.Clear();
            Console.WriteLine("=============== Desmarcar Tarefa como Concluída ===============");

            if (tarefas.Count == 0)
            {
                Console.WriteLine("Nenhuma tarefa encontrada.");
                Console.WriteLine("Pressione qualquer tecla para continuar...");
                Console.ReadKey();
                return;
            }

            ListarTarefasComIndice();
            Console.Write("Digite o número da tarefa para desmarcar como concluída: ");

            if (int.TryParse(Console.ReadLine(), out int indice) && indice > 0 && indice <= tarefas.Count)
            {
                tarefas[indice - 1].Concluida = false;
                Console.WriteLine("Tarefa desmarcada como concluída!");
            }
            else
            {
                Console.WriteLine("Número inválido!");
            }

            Console.WriteLine("Pressione qualquer tecla para continuar...");
            Console.ReadKey();
        }

        static void RemoverTarefa()
        {
            Console.Clear();
            Console.WriteLine("=============== Remover Tarefa ===============");

            if (tarefas.Count == 0)
            {
                Console.WriteLine("Nenhuma tarefa encontrada.");
                Console.WriteLine("Pressione qualquer tecla para continuar...");
                Console.ReadKey();
                return;
            }

            ListarTarefasComIndice();
            Console.Write("Digite o número da tarefa para remover: ");

            if (int.TryParse(Console.ReadLine(), out int indice) && indice > 0 && indice <= tarefas.Count)
            {
                tarefas.RemoveAt(indice - 1);
                Console.WriteLine("Tarefa removida com sucesso!");
            }
            else
            {
                Console.WriteLine("Número inválido!");
            }

            Console.WriteLine("Pressione qualquer tecla para continuar...");
            Console.ReadKey();
        }

        static void ListarTarefasComIndice()
        {
            Console.WriteLine("=============== Lista de Tarefas ===============");

            for (int i = 0; i < tarefas.Count; i++)
            {
                Console.WriteLine($"\n--- Tarefa {i + 1} ---");
                Console.WriteLine(tarefas[i].ToString());
            }
            Console.WriteLine();
        }
    }
}