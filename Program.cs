using System;
using System.Globalization;
namespace Course
{
    class Program
    {
        static void Main(string[] args)
        {
            bool executando = true;
            while (executando)
            {
                Console.Clear();
                Console.WriteLine("====================== Gerenciador de Tarefas ======================");
                Console.Write("1 - Adicionar nova Tarefa\n2 - Listar Tarefas\n3 - Marcar Tarefa como Concluida\n4 - Desmarcar Tarefa como Concluida\n5 - Remover Tarefa\n6 - Sair\nEscolha uma opção: ");

                string opcao = Console.ReadLine();

                switch (opcao)
                {
                    case "1":
                        AdicionarTarefa();
                        break;
                    case "2":
                        ListarTarefas();
                        break;
                    case "3":
                        MarcarConcluida();
                        break;
                    case "4":
                        DesmarcarConcluida();
                        break;
                    case "5":
                        RemoverTarefa();
                        break;
                    case "6":
                        executando = false;
                        break;
                    default:

                }
            }
        }
    }
}