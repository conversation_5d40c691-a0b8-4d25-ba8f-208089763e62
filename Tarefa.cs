public class Tarefa
{
    public string Titulo;
    public string <PERSON>cricao;
    public bool <PERSON><PERSON>luida;

    public Tarefa(string titulo, string descricao)
    {
        Titulo = titulo;
        Descricao = descricao;
        Concluida = false;
    }
    public override string ToString()
    {
        string status = Concluida ? "Concluida" : "Pendente";
        return $"Título: {Titulo}\n Descrição: {Descricao}\nStatus: {status}";
    }
    
}